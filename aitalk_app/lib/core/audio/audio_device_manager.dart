import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:audio_session/audio_session.dart';

/// 音频设备管理器
///
/// 负责管理音频输入输出设备的路由，尽可能分离录音和播放设备
/// 以减少回音问题
class AudioDeviceManager {
  AudioDeviceManager._();

  static final AudioDeviceManager instance = AudioDeviceManager._();

  AudioSession? _audioSession;
  bool _isInitialized = false;

  // 设备状态
  bool _isHeadsetConnected = false;
  bool _isBluetoothConnected = false;
  String _currentInputDevice = 'builtin_mic';
  String _currentOutputDevice = 'earpiece';

  StreamSubscription? _deviceChangeSubscription;

  /// 初始化音频设备管理器
  Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      debugPrint('🎧 初始化音频设备管理器');

      _audioSession = await AudioSession.instance;

      // 监听设备变化
      _setupDeviceChangeListener();

      // 检测当前连接的设备
      await _detectConnectedDevices();

      _isInitialized = true;
      debugPrint('✅ 音频设备管理器初始化成功');
      return true;
    } catch (e) {
      debugPrint('❌ 音频设备管理器初始化失败: $e');
      return false;
    }
  }

  /// 配置最佳的音频设备组合以避免回音
  Future<bool> configureOptimalDevices() async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      debugPrint('🎧 配置最佳音频设备组合');

      // 优先级策略：
      // 1. 有线耳机：输入输出都使用耳机（最佳，物理隔离）
      // 2. 蓝牙耳机：输入输出都使用蓝牙（较好，物理隔离）
      // 3. 内置设备：输入用麦克风，输出用听筒（需要软件回音消除）

      if (_isHeadsetConnected) {
        return await _configureHeadsetMode();
      } else if (_isBluetoothConnected) {
        return await _configureBluetoothMode();
      } else {
        return await _configureBuiltinMode();
      }
    } catch (e) {
      debugPrint('❌ 配置音频设备失败: $e');
      return false;
    }
  }

  /// 配置有线耳机模式
  Future<bool> _configureHeadsetMode() async {
    debugPrint('🎧 配置有线耳机模式（输入输出物理隔离）');

    await _audioSession!.configure(
      AudioSessionConfiguration(
        avAudioSessionCategory: AVAudioSessionCategory.playAndRecord,
        avAudioSessionCategoryOptions:
            AVAudioSessionCategoryOptions.allowBluetooth,
        avAudioSessionMode: AVAudioSessionMode.voiceChat,

        androidAudioAttributes: const AndroidAudioAttributes(
          contentType: AndroidAudioContentType.speech,
          flags: AndroidAudioFlags.none,
          usage: AndroidAudioUsage.voiceCommunication,
        ),
        androidAudioFocusGainType: AndroidAudioFocusGainType.gain,
      ),
    );

    _currentInputDevice = 'wired_headset';
    _currentOutputDevice = 'wired_headset';

    debugPrint('✅ 有线耳机模式配置完成');
    return true;
  }

  /// 配置蓝牙耳机模式
  Future<bool> _configureBluetoothMode() async {
    debugPrint('🎧 配置蓝牙耳机模式（输入输出物理隔离）');

    await _audioSession!.configure(
      AudioSessionConfiguration(
        avAudioSessionCategory: AVAudioSessionCategory.playAndRecord,
        avAudioSessionCategoryOptions:
            AVAudioSessionCategoryOptions.allowBluetooth,
        avAudioSessionMode: AVAudioSessionMode.voiceChat,

        androidAudioAttributes: const AndroidAudioAttributes(
          contentType: AndroidAudioContentType.speech,
          flags: AndroidAudioFlags.none,
          usage: AndroidAudioUsage.voiceCommunication,
        ),
        androidAudioFocusGainType: AndroidAudioFocusGainType.gain,
      ),
    );

    _currentInputDevice = 'bluetooth';
    _currentOutputDevice = 'bluetooth';

    debugPrint('✅ 蓝牙耳机模式配置完成');
    return true;
  }

  /// 配置内置设备模式（需要软件回音消除）
  Future<bool> _configureBuiltinMode() async {
    debugPrint('🎧 配置内置设备模式（麦克风+听筒，需要回音消除）');

    await _audioSession!.configure(
      AudioSessionConfiguration(
        avAudioSessionCategory: AVAudioSessionCategory.playAndRecord,
        avAudioSessionCategoryOptions: AVAudioSessionCategoryOptions.none,
        avAudioSessionMode: AVAudioSessionMode.voiceChat,

        androidAudioAttributes: const AndroidAudioAttributes(
          contentType: AndroidAudioContentType.speech,
          flags: AndroidAudioFlags.none,
          usage: AndroidAudioUsage.voiceCommunication,
        ),
        androidAudioFocusGainType: AndroidAudioFocusGainType.gain,
      ),
    );

    _currentInputDevice = 'builtin_mic';
    _currentOutputDevice = 'earpiece';

    debugPrint('⚠️ 使用内置设备，建议启用软件回音消除');
    return true;
  }

  /// 检测连接的音频设备
  Future<void> _detectConnectedDevices() async {
    try {
      // 这里可以添加平台特定的设备检测逻辑
      // 目前使用简化的检测方法

      if (Platform.isIOS) {
        // iOS设备检测逻辑
        await _detectIOSDevices();
      } else if (Platform.isAndroid) {
        // Android设备检测逻辑
        await _detectAndroidDevices();
      }

      debugPrint('🎧 设备检测完成:');
      debugPrint('  - 有线耳机: $_isHeadsetConnected');
      debugPrint('  - 蓝牙设备: $_isBluetoothConnected');
      debugPrint('  - 输入设备: $_currentInputDevice');
      debugPrint('  - 输出设备: $_currentOutputDevice');
    } catch (e) {
      debugPrint('❌ 设备检测失败: $e');
    }
  }

  /// iOS设备检测
  Future<void> _detectIOSDevices() async {
    // 简化的检测逻辑，实际应用中可以使用更精确的方法
    // 例如通过AVAudioSession的currentRoute属性
    _isHeadsetConnected = false; // 需要实际检测
    _isBluetoothConnected = false; // 需要实际检测
  }

  /// Android设备检测
  Future<void> _detectAndroidDevices() async {
    // 简化的检测逻辑，实际应用中可以使用AudioManager
    _isHeadsetConnected = false; // 需要实际检测
    _isBluetoothConnected = false; // 需要实际检测
  }

  /// 设置设备变化监听
  void _setupDeviceChangeListener() {
    // 监听音频设备变化
    _deviceChangeSubscription = _audioSession?.interruptionEventStream.listen((
      event,
    ) {
      debugPrint('🎧 音频中断事件: ${event.type}');
      // 中断结束后重新检测设备
      _detectConnectedDevices();
    });
  }

  /// 获取当前设备配置
  Map<String, dynamic> getCurrentConfiguration() {
    return {
      'inputDevice': _currentInputDevice,
      'outputDevice': _currentOutputDevice,
      'isHeadsetConnected': _isHeadsetConnected,
      'isBluetoothConnected': _isBluetoothConnected,
      'needsEchoCancellation': _needsEchoCancellation(),
    };
  }

  /// 判断是否需要软件回音消除
  bool _needsEchoCancellation() {
    // 如果输入输出使用相同的内置设备，需要回音消除
    return _currentInputDevice == 'builtin_mic' &&
        _currentOutputDevice == 'earpiece';
  }

  /// 是否需要回音消除
  bool get needsEchoCancellation => _needsEchoCancellation();

  /// 获取当前输入设备
  String get currentInputDevice => _currentInputDevice;

  /// 获取当前输出设备
  String get currentOutputDevice => _currentOutputDevice;

  /// 释放资源
  Future<void> dispose() async {
    await _deviceChangeSubscription?.cancel();
    _deviceChangeSubscription = null;
    _isInitialized = false;
  }
}
