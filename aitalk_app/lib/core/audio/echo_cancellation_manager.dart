import 'dart:async';
import 'dart:typed_data';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';

/// 回音消除管理器
/// 
/// 提供软件级别的回音消除功能，通过分析播放和录音信号
/// 来检测和消除回音
class EchoCancellationManager {
  EchoCancellationManager._();
  
  static final EchoCancellationManager instance = EchoCancellationManager._();
  
  // 回音消除参数
  static const int _maxEchoDelay = 8000; // 最大回音延迟（采样点数，约1秒@8kHz）
  static const double _echoThreshold = 0.3; // 回音检测阈值
  static const double _suppressionFactor = 0.1; // 回音抑制因子
  
  // 播放信号缓存（用于回音检测）
  final List<double> _playbackBuffer = [];
  
  // 自适应滤波器参数
  final List<double> _adaptiveFilter = List.filled(256, 0.0);
  static const double _learningRate = 0.001;
  
  // 能量检测
  double _playbackEnergy = 0.0;
  double _recordEnergy = 0.0;
  
  bool _isEnabled = true;
  
  /// 启用或禁用回音消除
  void setEnabled(bool enabled) {
    _isEnabled = enabled;
    if (!enabled) {
      reset();
    }
    debugPrint('🔇 回音消除${enabled ? "已启用" : "已禁用"}');
  }
  
  /// 记录播放的音频数据（用于回音检测）
  void recordPlaybackAudio(Int16List pcmData) {
    if (!_isEnabled) return;
    
    // 转换为double并添加到播放缓存
    final doubleData = pcmData.map((sample) => sample.toDouble()).toList();
    _playbackBuffer.addAll(doubleData);
    
    // 限制缓存大小
    while (_playbackBuffer.length > _maxEchoDelay) {
      _playbackBuffer.removeAt(0);
    }
    
    // 计算播放能量
    _playbackEnergy = _calculateEnergy(doubleData);
  }
  
  /// 处理录音音频数据，移除回音
  Int16List processRecordedAudio(Int16List pcmData) {
    if (!_isEnabled || _playbackBuffer.isEmpty) {
      return pcmData;
    }
    
    try {
      // 转换为double进行处理
      final doubleData = pcmData.map((sample) => sample.toDouble()).toList();
      
      // 计算录音能量
      _recordEnergy = _calculateEnergy(doubleData);
      
      // 如果播放能量很低，不需要回音消除
      if (_playbackEnergy < 1000) {
        return pcmData;
      }
      
      // 执行回音消除
      final processedData = _performEchoCancellation(doubleData);
      
      // 转换回Int16List
      return Int16List.fromList(
        processedData.map((sample) => sample.clamp(-32768, 32767).round()).toList()
      );
    } catch (e) {
      debugPrint('❌ 回音消除处理失败: $e');
      return pcmData;
    }
  }
  
  /// 执行回音消除算法
  List<double> _performEchoCancellation(List<double> recordedData) {
    final processedData = <double>[];
    
    for (int i = 0; i < recordedData.length; i++) {
      double sample = recordedData[i];
      
      // 简单的自适应回音消除
      if (_playbackBuffer.length > 100) {
        // 计算与播放信号的相关性
        final correlation = _calculateCorrelation(
          recordedData, 
          i, 
          _playbackBuffer, 
          _playbackBuffer.length - 100
        );
        
        // 如果相关性高，说明可能是回音
        if (correlation.abs() > _echoThreshold) {
          // 应用回音抑制
          sample *= (1.0 - _suppressionFactor * correlation.abs());
        }
      }
      
      processedData.add(sample);
    }
    
    return processedData;
  }
  
  /// 计算两个信号段的相关性
  double _calculateCorrelation(
    List<double> signal1, 
    int start1,
    List<double> signal2, 
    int start2
  ) {
    const windowSize = 64;
    double correlation = 0.0;
    
    for (int i = 0; i < windowSize && 
         start1 + i < signal1.length && 
         start2 + i < signal2.length; i++) {
      correlation += signal1[start1 + i] * signal2[start2 + i];
    }
    
    return correlation / windowSize;
  }
  
  /// 计算信号能量
  double _calculateEnergy(List<double> signal) {
    double energy = 0.0;
    for (double sample in signal) {
      energy += sample * sample;
    }
    return energy / signal.length;
  }
  
  /// 重置回音消除状态
  void reset() {
    _playbackBuffer.clear();
    _adaptiveFilter.fillRange(0, _adaptiveFilter.length, 0.0);
    _playbackEnergy = 0.0;
    _recordEnergy = 0.0;
    debugPrint('🔇 回音消除状态已重置');
  }
  
  /// 获取当前状态信息
  Map<String, dynamic> getStatus() {
    return {
      'enabled': _isEnabled,
      'playbackBufferSize': _playbackBuffer.length,
      'playbackEnergy': _playbackEnergy,
      'recordEnergy': _recordEnergy,
    };
  }
}
