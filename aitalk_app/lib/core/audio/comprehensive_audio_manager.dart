import 'dart:async';
import 'package:flutter/foundation.dart';

import 'audio_device_manager.dart';
import 'echo_cancellation_manager.dart';
import 'advanced_audio_router.dart';
import 'call_audio_session_manager.dart';
import '../services/settings_service.dart';

/// 综合音频管理器
/// 
/// 整合所有音频处理策略，提供最佳的回音消除解决方案
class ComprehensiveAudioManager {
  ComprehensiveAudioManager._();
  
  static final ComprehensiveAudioManager instance = ComprehensiveAudioManager._();
  
  final AudioDeviceManager _deviceManager = AudioDeviceManager.instance;
  final EchoCancellationManager _echoCancellation = EchoCancellationManager.instance;
  final AdvancedAudioRouter _audioRouter = AdvancedAudioRouter.instance;
  
  bool _isInitialized = false;
  String _currentStrategy = 'unknown';
  
  /// 初始化综合音频管理器
  Future<bool> initialize() async {
    if (_isInitialized) return true;
    
    try {
      debugPrint('🎧 初始化综合音频管理器');
      
      // 初始化各个组件
      await _deviceManager.initialize();
      await _audioRouter.initialize();
      
      _isInitialized = true;
      debugPrint('✅ 综合音频管理器初始化成功');
      return true;
    } catch (e) {
      debugPrint('❌ 综合音频管理器初始化失败: $e');
      return false;
    }
  }
  
  /// 配置最佳的音频策略以避免回音
  Future<bool> configureOptimalAudioStrategy() async {
    if (!_isInitialized) {
      await initialize();
    }
    
    try {
      debugPrint('🎧 配置最佳音频策略');
      
      // 获取用户设置
      final enableEchoCancellation = await SettingsService.getCallEnableEchoCancellation() ?? true;
      final enableNoiseSuppression = await SettingsService.getCallEnableNoiseSuppression() ?? true;
      
      // 获取可用设备
      final availableDevices = await _audioRouter.getAvailableDevices();
      
      debugPrint('🎧 可用设备: $availableDevices');
      debugPrint('🎧 用户设置 - 回音消除: $enableEchoCancellation, 噪音抑制: $enableNoiseSuppression');
      
      // 策略优先级：
      // 1. 有线耳机 - 物理隔离，最佳选择
      // 2. 蓝牙耳机 - 物理隔离，较好选择
      // 3. 高级音频路由 - 尝试设备分离
      // 4. 标准配置 + 软件回音消除
      
      bool success = false;
      
      // 策略1：尝试有线耳机
      if (availableDevices['inputDevices']!.contains('wired_headset') &&
          availableDevices['outputDevices']!.contains('wired_headset')) {
        success = await _configureWiredHeadsetStrategy();
        if (success) {
          _currentStrategy = 'wired_headset';
          debugPrint('✅ 采用有线耳机策略');
        }
      }
      
      // 策略2：尝试蓝牙耳机
      if (!success && 
          availableDevices['inputDevices']!.contains('bluetooth') &&
          availableDevices['outputDevices']!.contains('bluetooth')) {
        success = await _configureBluetoothStrategy();
        if (success) {
          _currentStrategy = 'bluetooth';
          debugPrint('✅ 采用蓝牙耳机策略');
        }
      }
      
      // 策略3：尝试高级音频路由
      if (!success) {
        success = await _configureAdvancedRoutingStrategy();
        if (success) {
          _currentStrategy = 'advanced_routing';
          debugPrint('✅ 采用高级音频路由策略');
        }
      }
      
      // 策略4：标准配置 + 软件回音消除
      if (!success) {
        success = await _configureStandardWithEchoCancellationStrategy();
        if (success) {
          _currentStrategy = 'standard_with_aec';
          debugPrint('✅ 采用标准配置+软件回音消除策略');
        }
      }
      
      // 配置软件回音消除
      if (enableEchoCancellation && _needsSoftwareEchoCancellation()) {
        _echoCancellation.setEnabled(true);
        debugPrint('🔇 启用软件回音消除');
      } else {
        _echoCancellation.setEnabled(false);
        debugPrint('🔇 禁用软件回音消除（硬件已处理或用户禁用）');
      }
      
      return success;
    } catch (e) {
      debugPrint('❌ 配置音频策略失败: $e');
      return false;
    }
  }
  
  /// 配置有线耳机策略
  Future<bool> _configureWiredHeadsetStrategy() async {
    try {
      debugPrint('🎧 配置有线耳机策略');
      
      // 强制使用有线耳机
      final routerSuccess = await _audioRouter.forceWiredHeadset();
      
      // 配置音频会话
      final sessionSuccess = await CallAudioSessionManager.instance.configureForCall();
      
      return routerSuccess && sessionSuccess;
    } catch (e) {
      debugPrint('❌ 有线耳机策略配置失败: $e');
      return false;
    }
  }
  
  /// 配置蓝牙耳机策略
  Future<bool> _configureBluetoothStrategy() async {
    try {
      debugPrint('🎧 配置蓝牙耳机策略');
      
      // 强制使用蓝牙设备
      final routerSuccess = await _audioRouter.forceBluetoothDevices();
      
      // 配置音频会话
      final sessionSuccess = await CallAudioSessionManager.instance.configureForCall();
      
      return routerSuccess && sessionSuccess;
    } catch (e) {
      debugPrint('❌ 蓝牙耳机策略配置失败: $e');
      return false;
    }
  }
  
  /// 配置高级音频路由策略
  Future<bool> _configureAdvancedRoutingStrategy() async {
    try {
      debugPrint('🎧 配置高级音频路由策略');
      
      // 尝试分离音频设备
      final routerSuccess = await _audioRouter.configureSeparatedDevices();
      
      // 配置音频会话
      final sessionSuccess = await CallAudioSessionManager.instance.configureForCall();
      
      return routerSuccess && sessionSuccess;
    } catch (e) {
      debugPrint('❌ 高级音频路由策略配置失败: $e');
      return false;
    }
  }
  
  /// 配置标准配置+软件回音消除策略
  Future<bool> _configureStandardWithEchoCancellationStrategy() async {
    try {
      debugPrint('🎧 配置标准配置+软件回音消除策略');
      
      // 使用标准设备管理器配置
      final deviceSuccess = await _deviceManager.configureOptimalDevices();
      
      // 配置音频会话
      final sessionSuccess = await CallAudioSessionManager.instance.configureForCall();
      
      return deviceSuccess && sessionSuccess;
    } catch (e) {
      debugPrint('❌ 标准配置策略配置失败: $e');
      return false;
    }
  }
  
  /// 判断是否需要软件回音消除
  bool _needsSoftwareEchoCancellation() {
    switch (_currentStrategy) {
      case 'wired_headset':
      case 'bluetooth':
        // 物理隔离的设备不需要软件回音消除
        return false;
      case 'advanced_routing':
        // 检查高级路由是否成功分离设备
        return _audioRouter.needsSoftwareEchoCancellation();
      case 'standard_with_aec':
      default:
        // 标准配置需要软件回音消除
        return true;
    }
  }
  
  /// 获取当前音频配置状态
  Map<String, dynamic> getCurrentStatus() {
    return {
      'isInitialized': _isInitialized,
      'currentStrategy': _currentStrategy,
      'deviceManager': _deviceManager.getCurrentConfiguration(),
      'audioRouter': _audioRouter.getCurrentConfiguration(),
      'echoCancellation': _echoCancellation.getStatus(),
      'needsSoftwareEchoCancellation': _needsSoftwareEchoCancellation(),
    };
  }
  
  /// 重置所有音频组件
  Future<void> reset() async {
    try {
      debugPrint('🎧 重置综合音频管理器');
      
      _echoCancellation.reset();
      _currentStrategy = 'unknown';
      
      debugPrint('✅ 综合音频管理器已重置');
    } catch (e) {
      debugPrint('❌ 重置综合音频管理器失败: $e');
    }
  }
  
  /// 获取当前策略
  String get currentStrategy => _currentStrategy;
  
  /// 获取回音消除管理器
  EchoCancellationManager get echoCancellation => _echoCancellation;
  
  /// 释放资源
  Future<void> dispose() async {
    await _deviceManager.dispose();
    await _audioRouter.dispose();
    _isInitialized = false;
  }
}
