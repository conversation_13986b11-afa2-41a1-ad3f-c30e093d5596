import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

/// 高级音频路由管理器
/// 
/// 提供更精确的音频设备控制，尝试实现录音和播放设备的物理分离
class AdvancedAudioRouter {
  AdvancedAudioRouter._();
  
  static final AdvancedAudioRouter instance = AdvancedAudioRouter._();
  
  static const MethodChannel _channel = MethodChannel('aitalk/audio_router');
  
  bool _isInitialized = false;
  String _currentInputRoute = 'unknown';
  String _currentOutputRoute = 'unknown';
  
  /// 初始化音频路由管理器
  Future<bool> initialize() async {
    if (_isInitialized) return true;
    
    try {
      debugPrint('🎧 初始化高级音频路由管理器');
      
      // 注册平台方法调用处理器
      _channel.setMethodCallHandler(_handleMethodCall);
      
      // 获取当前音频路由信息
      await _updateCurrentRoutes();
      
      _isInitialized = true;
      debugPrint('✅ 高级音频路由管理器初始化成功');
      return true;
    } catch (e) {
      debugPrint('❌ 高级音频路由管理器初始化失败: $e');
      return false;
    }
  }
  
  /// 配置分离的音频设备
  /// 
  /// 尝试将录音和播放路由到不同的设备以避免回音
  Future<bool> configureSeparatedDevices() async {
    if (!_isInitialized) {
      await initialize();
    }
    
    try {
      debugPrint('🎧 配置分离的音频设备');
      
      if (Platform.isIOS) {
        return await _configureIOSSeparatedDevices();
      } else if (Platform.isAndroid) {
        return await _configureAndroidSeparatedDevices();
      }
      
      return false;
    } catch (e) {
      debugPrint('❌ 配置分离音频设备失败: $e');
      return false;
    }
  }
  
  /// iOS设备分离配置
  Future<bool> _configureIOSSeparatedDevices() async {
    try {
      // 尝试设置录音使用底部麦克风，播放使用听筒
      final result = await _channel.invokeMethod('configureIOSDevices', {
        'inputPreference': 'bottom_mic',
        'outputPreference': 'earpiece',
        'enableEchoCancellation': true,
        'enableNoiseSuppression': true,
      });
      
      if (result['success'] == true) {
        _currentInputRoute = result['inputRoute'] ?? 'unknown';
        _currentOutputRoute = result['outputRoute'] ?? 'unknown';
        
        debugPrint('✅ iOS设备分离配置成功:');
        debugPrint('  - 输入: $_currentInputRoute');
        debugPrint('  - 输出: $_currentOutputRoute');
        return true;
      }
      
      return false;
    } catch (e) {
      debugPrint('❌ iOS设备分离配置失败: $e');
      return false;
    }
  }
  
  /// Android设备分离配置
  Future<bool> _configureAndroidSeparatedDevices() async {
    try {
      // 尝试设置录音使用主麦克风，播放使用听筒
      final result = await _channel.invokeMethod('configureAndroidDevices', {
        'inputSource': 'mic',
        'outputDevice': 'earpiece',
        'enableAEC': true, // Acoustic Echo Cancellation
        'enableNS': true,  // Noise Suppression
        'enableAGC': true, // Automatic Gain Control
      });
      
      if (result['success'] == true) {
        _currentInputRoute = result['inputRoute'] ?? 'unknown';
        _currentOutputRoute = result['outputRoute'] ?? 'unknown';
        
        debugPrint('✅ Android设备分离配置成功:');
        debugPrint('  - 输入: $_currentInputRoute');
        debugPrint('  - 输出: $_currentOutputRoute');
        return true;
      }
      
      return false;
    } catch (e) {
      debugPrint('❌ Android设备分离配置失败: $e');
      return false;
    }
  }
  
  /// 强制使用蓝牙设备（如果可用）
  Future<bool> forceBluetoothDevices() async {
    try {
      debugPrint('🎧 强制使用蓝牙设备');
      
      final result = await _channel.invokeMethod('forceBluetoothDevices');
      
      if (result['success'] == true) {
        _currentInputRoute = 'bluetooth';
        _currentOutputRoute = 'bluetooth';
        
        debugPrint('✅ 蓝牙设备配置成功');
        return true;
      }
      
      return false;
    } catch (e) {
      debugPrint('❌ 蓝牙设备配置失败: $e');
      return false;
    }
  }
  
  /// 强制使用有线耳机（如果可用）
  Future<bool> forceWiredHeadset() async {
    try {
      debugPrint('🎧 强制使用有线耳机');
      
      final result = await _channel.invokeMethod('forceWiredHeadset');
      
      if (result['success'] == true) {
        _currentInputRoute = 'wired_headset';
        _currentOutputRoute = 'wired_headset';
        
        debugPrint('✅ 有线耳机配置成功');
        return true;
      }
      
      return false;
    } catch (e) {
      debugPrint('❌ 有线耳机配置失败: $e');
      return false;
    }
  }
  
  /// 获取可用的音频设备列表
  Future<Map<String, List<String>>> getAvailableDevices() async {
    try {
      final result = await _channel.invokeMethod('getAvailableDevices');
      
      return {
        'inputDevices': List<String>.from(result['inputDevices'] ?? []),
        'outputDevices': List<String>.from(result['outputDevices'] ?? []),
      };
    } catch (e) {
      debugPrint('❌ 获取可用设备失败: $e');
      return {
        'inputDevices': [],
        'outputDevices': [],
      };
    }
  }
  
  /// 检查是否需要软件回音消除
  bool needsSoftwareEchoCancellation() {
    // 如果输入输出使用相同的内置设备，需要软件回音消除
    return _currentInputRoute.contains('mic') && 
           _currentOutputRoute.contains('earpiece');
  }
  
  /// 更新当前音频路由信息
  Future<void> _updateCurrentRoutes() async {
    try {
      final result = await _channel.invokeMethod('getCurrentRoutes');
      
      _currentInputRoute = result['inputRoute'] ?? 'unknown';
      _currentOutputRoute = result['outputRoute'] ?? 'unknown';
      
      debugPrint('🎧 当前音频路由:');
      debugPrint('  - 输入: $_currentInputRoute');
      debugPrint('  - 输出: $_currentOutputRoute');
    } catch (e) {
      debugPrint('❌ 更新音频路由信息失败: $e');
    }
  }
  
  /// 处理来自原生平台的方法调用
  Future<dynamic> _handleMethodCall(MethodCall call) async {
    switch (call.method) {
      case 'onAudioRouteChanged':
        final arguments = call.arguments as Map<String, dynamic>;
        _currentInputRoute = arguments['inputRoute'] ?? _currentInputRoute;
        _currentOutputRoute = arguments['outputRoute'] ?? _currentOutputRoute;
        
        debugPrint('🎧 音频路由已变更:');
        debugPrint('  - 输入: $_currentInputRoute');
        debugPrint('  - 输出: $_currentOutputRoute');
        break;
        
      default:
        debugPrint('⚠️ 未知的方法调用: ${call.method}');
    }
  }
  
  /// 获取当前配置信息
  Map<String, dynamic> getCurrentConfiguration() {
    return {
      'inputRoute': _currentInputRoute,
      'outputRoute': _currentOutputRoute,
      'needsSoftwareEchoCancellation': needsSoftwareEchoCancellation(),
      'isInitialized': _isInitialized,
    };
  }
  
  /// 获取当前输入路由
  String get currentInputRoute => _currentInputRoute;
  
  /// 获取当前输出路由
  String get currentOutputRoute => _currentOutputRoute;
  
  /// 释放资源
  Future<void> dispose() async {
    _isInitialized = false;
  }
}
